"use client";

import { useActionState, useEffect, useRef } from "react";
import { submitContactForm } from "@/actions/form";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { FormField } from "@/components/forms";
import { toast } from "sonner";
import type { FormSubmissionResult } from "@/lib/types";
import {
  AlertCircle,
  CheckCircle2,
  Mail,
  User,
  Building,
  Phone,
  Globe,
} from "lucide-react";

function FormPage() {
  const [state, formAction, isPending] = useActionState<
    FormSubmissionResult | null,
    FormData
  >(submitContactForm, null);

  const formRef = useRef<HTMLFormElement>(null);

  // Show toast when state changes
  useEffect(() => {
    if (state) {
      if (state.success) {
        toast.success("Form submitted successfully!", {
          description: state.message,
          icon: <CheckCircle2 className="h-4 w-4" />,
        });
        // Reset form on success
        formRef.current?.reset();
      } else {
        // Only show toast for non-validation errors
        if (!state.errors) {
          toast.error("Error submitting form", {
            description: state.message,
            icon: <AlertCircle className="h-4 w-4" />,
          });
        }
      }
    }
  }, [state]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="w-full max-w-2xl space-y-6">
        <Card>
          <CardHeader className="text-center">
            <CardTitle className="text-3xl font-bold">Contact Us</CardTitle>
            <CardDescription>
              Get in touch with us. We&apos;d love to hear from you!
            </CardDescription>
          </CardHeader>

          <CardContent>
            <form ref={formRef} action={formAction} className="space-y-6">
              {/* Show general error message for non-field errors */}
              {state && !state.success && !state.errors && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{state.message}</AlertDescription>
                </Alert>
              )}

              {/* Show validation error summary */}
              {state?.errors && Object.keys(state.errors).length > 0 && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Please correct the errors below and try again.
                  </AlertDescription>
                </Alert>
              )}

              {/* Success message */}
              {state?.success && (
                <Alert className="border-green-500 bg-green-50">
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                  <AlertDescription className="text-green-800">
                    {state.message}
                    {state.timestamp && (
                      <span className="block text-xs text-green-600 mt-1">
                        Sent at {new Date(state.timestamp).toLocaleString()}
                      </span>
                    )}
                  </AlertDescription>
                </Alert>
              )}

              {/* Required Fields */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Required Information</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    label="Name"
                    name="name"
                    placeholder="Your full name"
                    required
                    disabled={isPending}
                    errors={state?.errors?.name}
                    icon={<User className="h-4 w-4" />}
                  />

                  <FormField
                    label="Email"
                    name="email"
                    type="email"
                    placeholder="<EMAIL>"
                    required
                    disabled={isPending}
                    errors={state?.errors?.email}
                    icon={<Mail className="h-4 w-4" />}
                  />
                </div>

                <FormField
                  label="Message"
                  name="message"
                  placeholder="Tell us how we can help you..."
                  required
                  disabled={isPending}
                  errors={state?.errors?.message}
                  isTextarea
                  rows={4}
                  description="Please provide details about your inquiry"
                />
              </div>

              {/* Optional Fields */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">
                  Additional Information (Optional)
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    label="Company"
                    name="company"
                    placeholder="Your company name"
                    disabled={isPending}
                    errors={state?.errors?.company}
                    icon={<Building className="h-4 w-4" />}
                  />

                  <FormField
                    label="Job Title"
                    name="job_title"
                    placeholder="Your position"
                    disabled={isPending}
                    errors={state?.errors?.jobTitle}
                    icon={<User className="h-4 w-4" />}
                  />

                  <FormField
                    label="Phone"
                    name="phone"
                    type="tel"
                    placeholder="+****************"
                    disabled={isPending}
                    errors={state?.errors?.phone}
                    icon={<Phone className="h-4 w-4" />}
                  />

                  <FormField
                    label="Website"
                    name="website"
                    type="url"
                    placeholder="https://yourwebsite.com"
                    disabled={isPending}
                    errors={state?.errors?.website}
                    icon={<Globe className="h-4 w-4" />}
                  />
                </div>
              </div>

              <Button type="submit" disabled={isPending} className="w-full">
                {isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Sending...
                  </>
                ) : (
                  "Send Message"
                )}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default FormPage;
