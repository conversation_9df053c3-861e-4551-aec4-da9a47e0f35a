"use server";

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";

import { createClient } from "@/utils/supabase/server";

export async function login(formData: FormData) {
  const supabase = await createClient();

  // type-casting here for convenience
  // in practice, you should validate your inputs
  const data = {
    email: formData.get("email") as string,
    password: formData.get("password") as string,
  };

  const { error, data: result } = await supabase.auth.signInWithPassword(data);
  console.log("Login result:", result);

  if (error) {
    console.error("Login error:", error);
    redirect("/error");
  }

  // Ensure we have a valid session
  if (!result.session) {
    console.error("No session after login");
    redirect("/error");
  }

  revalidatePath("/", "layout");
  redirect("/private");
}

export async function signup(formData: FormData) {
  const supabase = await createClient();

  // type-casting here for convenience
  // in practice, you should validate your inputs
  const data = {
    email: formData.get("email") as string,
    password: formData.get("password") as string,
  };

  const { data: signUpData, error } = await supabase.auth.signUp(data);
  console.log("Signup result:", signUpData);

  if (error) {
    console.error("Signup error:", error);
    redirect("/error");
  }

  // Check if email confirmation is required
  if (signUpData.user && !signUpData.session) {
    // User was created but needs to confirm email
    console.log("Email confirmation required");
    redirect("/auth/signup-success");
  }

  // User was created and is logged in (email confirmation disabled)
  revalidatePath("/", "layout");
  redirect("/private");
}

export async function logout() {
  const supabase = await createClient();

  const { error } = await supabase.auth.signOut();

  if (error) {
    redirect("/error");
  }

  revalidatePath("/", "layout");
  redirect("/login");
}
