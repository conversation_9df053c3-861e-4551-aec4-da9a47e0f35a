"use client";

import { useActionState, useEffect, useRef } from "react";
import { crawlWebsite } from "@/actions/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import ReactMarkdown from "react-markdown";

import { AlertCircle, CheckCircle2 } from "lucide-react";

interface FormFieldProps {
  label: string;
  name: string;
  type?: string;
  placeholder: string;
  required?: boolean;
  disabled?: boolean;
  errors?: string[];
  isTextarea?: boolean;
  rows?: number;
}

function FormField({
  label,
  name,
  type = "text",
  placeholder,
  required = false,
  disabled = false,
  errors = [],
  isTextarea = false,
  rows = 4,
}: FormFieldProps) {
  const hasErrors = errors.length > 0;
  const fieldId = `field-${name}`;

  return (
    <div className="space-y-2">
      <Label htmlFor={fieldId} className="text-sm font-medium">
        {label} {required && <span className="text-red-500">*</span>}
      </Label>

      {isTextarea ? (
        <Textarea
          id={fieldId}
          name={name}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          rows={rows}
          className={hasErrors ? "border-red-500 focus:border-red-500" : ""}
          aria-describedby={hasErrors ? `${fieldId}-error` : undefined}
        />
      ) : (
        <Input
          id={fieldId}
          type={type}
          name={name}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          className={hasErrors ? "border-red-500 focus:border-red-500" : ""}
          aria-describedby={hasErrors ? `${fieldId}-error` : undefined}
        />
      )}

      {hasErrors && (
        <div id={`${fieldId}-error`} className="space-y-1">
          {errors.map((error, index) => (
            <p
              key={index}
              className="text-sm text-red-600 flex items-center gap-1"
            >
              <AlertCircle className="h-3 w-3" />
              {error}
            </p>
          ))}
        </div>
      )}
    </div>
  );
}

function CrawlPage() {
  const [state, formAction, isPending] = useActionState<any, FormData>(
    crawlWebsite,
    null
  );

  console.log(state);

  const formRef = useRef<HTMLFormElement>(null);

  // Show toast when state changes
  useEffect(() => {
    if (state) {
      if (state.success) {
        toast.success("Form submitted successfully!", {
          description: state.message,
          icon: <CheckCircle2 className="h-4 w-4" />,
        });
        // Reset form on success
        formRef.current?.reset();
      } else {
        // Only show toast for non-validation errors
        if (!state.errors) {
          toast.error("Error submitting form", {
            description: state.message,
            icon: <AlertCircle className="h-4 w-4" />,
          });
        }
      }
    }
  }, [state]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="w-full max-w-md space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">Crawl Website</h1>
          <p className="text-muted-foreground">
            Extract and summarize content from any website
          </p>
        </div>

        <form ref={formRef} action={formAction} className="space-y-4">
          {/* Show general error message for non-field errors */}
          {state && !state.success && !state.errors && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{state.message}</AlertDescription>
            </Alert>
          )}

          {/* Show validation error summary */}
          {state?.errors && Object.keys(state.errors).length > 0 && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Please correct the errors below and try again.
              </AlertDescription>
            </Alert>
          )}

          <FormField
            label="Website URL"
            name="url"
            placeholder="https://example.com"
            required
            disabled={isPending}
            errors={state?.errors?.name}
          />

          <Button type="submit" disabled={isPending} className="w-full">
            {isPending ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Crawling...
              </>
            ) : (
              "Crawl Website"
            )}
          </Button>

          {/* Success message */}
          {state?.success && (
            <Alert className="border-green-500 bg-green-50">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                {state.message}
                {state.timestamp && (
                  <span className="block text-xs text-green-600 mt-1">
                    Crawled at {new Date(state.timestamp).toLocaleString()}
                  </span>
                )}
              </AlertDescription>
            </Alert>
          )}
        </form>

        {state?.data && (
          <details className="text-xs text-gray-500 mt-4">
            <summary className="cursor-pointer">Raw Markdown</summary>
            <div className="mt-2 p-2 rounded overflow-auto prose dark:prose-invert">
              <ReactMarkdown>{state.data.markdown}</ReactMarkdown>
            </div>
          </details>
        )}

        {state?.data && (
          <div>
            <h4 className="text-lg font-semibold mb-2">AI Summary</h4>
            <div className="mt-2 p-4 border rounded-lg overflow-auto prose dark:prose-invert">
              <ReactMarkdown>{state.data.summarization}</ReactMarkdown>
            </div>
          </div>
        )}

        {/* Development info */}
        {process.env.NODE_ENV === "development" && state && (
          <details className="text-xs text-gray-500 mt-4">
            <summary className="cursor-pointer">Debug Info</summary>
            <pre className="mt-2 p-2 bg-gray-100 rounded overflow-auto">
              {JSON.stringify(state, null, 2)}
            </pre>
          </details>
        )}
      </div>
    </div>
  );
}

export default CrawlPage;
