import Link from "next/link";
import { ModeToggle } from "./mode-toggle";
import { UserNav } from "./user-nav";

function Header() {
  return (
    <header className="sticky top-0 z-50 flex h-16 items-center justify-between border-b bg-background px-4">
      <h1 className="text-lg font-bold">
        <Link href="/">Vercel MCP Chat</Link>
      </h1>
      <nav>
        <ul className="flex items-center gap-4">
          <li>
            <Link href="/form">Contact</Link>
          </li>
          <li>
            <Link href="/crawl">Crawl Website</Link>
          </li>
          <li>
            <Link href="/forms">Forms</Link>
          </li>
          <li>
            <Link href="/private">Dashboard</Link>
          </li>
        </ul>
      </nav>
      <div className="flex items-center gap-2">
        <UserNav />
        <ModeToggle />
      </div>
    </header>
  );
}

export default Header;
